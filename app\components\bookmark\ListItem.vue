<script setup lang="ts">
import { useDateFormat, useTimeAgo } from '@vueuse/core'
import { getDomain, faviconURL } from '@/utils'
import { useVisibleFields } from '@/composables/useVisibleFields'

interface DataTableRowActionsProps {
  item: any
  selectedIds: string[]
  tags: any[]
  extra: {
    note: string
    emoji: string
  } | undefined
}
const props = defineProps<DataTableRowActionsProps>()

const emit = defineEmits<{
  dragStart: [id: string]
  openContextMenu: [open: boolean]
  delete: [id: string]
  edit: [id: string]
}>()

// 使用 composable 获取可见字段状态
const { visibleFields } = useVisibleFields()

// 添加一个 ref 来控制 HoverCard 的显示状态
const isHoverCardOpen = ref(false)

// 修改删除和编辑的 emit 事件，关闭 HoverCard
function handleDelete(id: string) {
  isHoverCardOpen.value = false
  emit('delete', id)
}

function handleEdit(id: string) {
  isHoverCardOpen.value = false
  emit('edit', id)
}
</script>

<template>
  <li class="relative">
    <div class="flex pl-5 py-1 pr-2 last:border-b-0 gap-4">
      
      <div class="flex items-center gap-4 flex-[1_1_50%] min-w-0">
        <img v-if="item.url" class="h-4 shrink-0" :src="faviconURL(item.url)" alt="">
        <Icon v-else name="lucide:folder" class="h-4 w-4 transition shrink-0" />
        <div class="min-w-0 flex-1">
          <div id="title" class="text-[13px] truncate">
            <span v-if="visibleFields.id">{{ item.id }} · </span>
            {{ item.title }}
          </div>
          <div 
            id="note" 
            v-if="extra?.note && visibleFields.note" 
            class="truncate mt-1 text-muted-foreground"
          >
            {{ extra?.note }}
          </div>
        </div>
      </div>
      
      <div 
        v-if="visibleFields.url" 
        class="flex items-center flex-[1_1_20%] min-w-0"
      >
        <a class="inline-block hover:underline" :href="item.url" target="_blank">
          {{ item.url ? getDomain(item.url) : '' }}
        </a>
      </div>
      <div 
        v-if="visibleFields.tags" 
        class="flex items-center flex-[1_1_30%] min-w-0"
      >
        <div class="truncate">
          <span 
            v-for="tag in tags" 
            :key="tag.id"
            class="px-2 py-1 text-xs rounded-full bg-secondary"
          >
            # {{ tag.name }}
          </span>
        </div>
      </div>
      <div v-if="visibleFields.emoji" id="emoji" class="flex items-center w-auto">
        {{ extra?.emoji }}
      </div>
      <HoverCard v-model:open="isHoverCardOpen" :openDelay="0" :closeDelay="0" :delayDuration="0">
      <HoverCardTrigger as-child>
        <Button variant="ghost" size="icon" class="h-8 w-8">
          <Icon name="lucide:ellipsis-vertical" />
        </Button>
      </HoverCardTrigger>
      <HoverCardContent align="end" side="top" class="w-auto max-w-[450px] min-w-[300px] gap-2 flex flex-col mr-8">
        <div class="text-sm text-muted-foreground border-b h-8 flex items-center pb-2">
          ID: {{ item.id }}
        </div>
        <div class="mt-2 text-sm text-muted-foreground border-b pb-4 mb-2">
          <img v-if="item.url" class="h-4 shrink-0 inline-block align-middle mr-2" :src="faviconURL(item.url)" alt="">
          <Icon v-if="!item.url" name="lucide:folder" class="h-4 w-4 transition shrink-0 inline-block align-middle mr-2" />
          <span class="text-base font-semibold align-middle">{{ item.title }}</span>
          <p v-if="item.url" class="text-sm text-muted-foreground mt-2">{{ item.url }}</p>
        </div>
        <div class="text-sm flex items-top gap-4">
          <span class="w-20 text-muted-foreground capitalize">{{ 'options.card.date_added' }}</span>
          <span class="text-foreground" :class="{ 'text-muted-foreground/75': !item.dateAdded }">{{ item.dateAdded ? useDateFormat(item.dateAdded, 'YYYY-MM-DD HH:mm:ss') : '-' }}</span>
        </div>
        <div class="text-sm flex items-top gap-4 capitalize">
          <span class="w-20 text-muted-foreground">{{ 'options.card.last_used' }}</span>
          <span class="text-foreground" :class="{ 'text-muted-foreground/75': !item.dateLastUsed }">{{ item.dateLastUsed ? useTimeAgo(item.dateLastUsed) : '-' }}</span>
        </div>
        <div class="text-sm flex items-center gap-4">
          <span class="w-20 text-muted-foreground capitalize">{{ 'options.card.tags' }}</span>
          <p class="flex items-center gap-2">
            <span v-for="tag in tags" :key="tag.id">
              # {{ tag.name }}
            </span>
            <span v-if="tags.length === 0" class="text-muted-foreground/75">-</span>
          </p>
        </div>
        <div class="text-sm flex items-top gap-4">
          <span class="w-20 text-muted-foreground capitalize">{{ 'options.card.note' }}</span>
          <span class="text-foreground" :class="{ 'text-muted-foreground/75': !extra?.note }">
            {{ extra?.note || '-' }}
          </span>
        </div>
        <div class="text-sm flex items-top gap-4">
          <span class="w-20 text-muted-foreground capitalize">{{ 'options.card.emoji' }}</span>
          <span class="text-foreground" :class="{ 'text-muted-foreground/75': !extra?.emoji }">{{ extra?.emoji || '-' }}</span>
        </div>
        <div class="mt-2 text-sm flex justify-between gap-4 border-t pt-4">
          <Button variant="outline" size="icon" class="h-8 w-8 text-muted-foreground/55 hover:text-foreground" @click="handleDelete(item.id)">
            <Icon name="lucide:trash-2" />
          </Button>
          <div class="flex items-center gap-2">
            <Button variant="outline" size="icon" class="h-8 w-8 text-muted-foreground/55 hover:text-foreground" as-child>
              <a :href="item.url" target="_blank">
                <Icon name="lucide:square-arrow-out-up-right" /> 
              </a>
            </Button>
            <Button variant="outline" size="icon" class="h-8 w-8 text-muted-foreground/55 hover:text-foreground" @click="handleEdit(item.id)">
              <Icon name="lucide:edit-3" />
            </Button>
          </div>
        </div>
      </HoverCardContent>
    </HoverCard>
    </div>
  </li>
</template>
