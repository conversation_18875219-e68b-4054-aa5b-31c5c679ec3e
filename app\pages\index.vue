<script setup lang="ts">
const themeStore = useThemeStore()
</script>

<template>
  <div class="pointer-events-none absolute inset-x-0 top-0 z-20 flex justify-center overflow-hidden">
    <div class="flex w-[108rem] flex-none justify-end">
      <picture><img
        src="/background/dark.bg.png"
        alt=""
        class="hidden w-[90rem] max-w-none flex-none dark:block"
        decoding="async"
      ></picture>
    </div>
  </div>
  <div
    class="absolute left-0 top-0 h-[700px] w-full bg-red-300 dark:hidden"
    style="background:linear-gradient(to bottom, #e7fffe, #fefbe9, transparent)"
  />

  <section class="container relative mb-60 mt-40">
    <HomeHero />
  </section>

  <!-- <section class="container mt-40">
    <HomeScreenshot src="/screenshots/admin_tasks.png" />
  </section> -->

  <section
    id="features"
    class="container mt-40"
  >
    <h2 class="font-gradient mb-3 text-center text-[3rem] leading-[120%] tracking-tight md:text-left md:text-[3.5rem]">
      Redefine <br>Bookmarking Experience
    </h2>
    <p class="mb-12 text-center text-base font-normal text-muted-foreground md:text-left md:text-[1.125rem] md:leading-normal">
      <span
        data-br=":r10:"
        data-brr="1"
        style="display: inline-block; vertical-align: top; text-decoration: inherit; text-wrap: balance;"
      >Bookmark manager extensions help you save, organize, and access your favorite websites with ease. <br>
        It allows you to categorize bookmarks into folders or tags, sync them across multiple devices, and includes features like notes, starred, and search.
      </span>
    </p>
    <HomeGridList />
  </section>

  <!-- <div
    id="pricing"
    class="w-full bg-background text-foreground"
    :class="`${themeStore.themeClass} dark`"
    :style="`--radius: ${themeStore.radius}rem;`"
  >
    <section class="container mt-20 relative max-w-5xl overflow-hidden rounded-3xl border-t py-24">
      <Background name="ball-light" />
      <h2 class="font-gradient mb-2 text-center text-5xl leading-[120%] tracking-tight">
        Simple pricing
      </h2>
      <p class="mb-24 text-center text-base text-muted-foreground md:text-lg md:leading-normal">
        Enhance your browser's built-in bookmark management with powerful new features.
      </p>
      <Pricing />
    </section>
  </div> -->

  <section
    id="faq"
    class="container mt-12 max-w-5xl"
  >
    <h2 class="font-gradient mb-2 text-center text-5xl leading-[120%] tracking-tight">
      Frequently asked questions
    </h2>
    <p class="mb-24 text-center text-base text-muted-foreground md:text-lg md:leading-normal">
      Have another question? Contact us by <NuxtLink
        href="/"
        class="text-foreground underline"
      >
        <EMAIL>
      </NuxtLink>.
    </p>
    <HomeFaq />
  </section>

  <section class="container relative mt-24 overflow-hidden pt-2">
    <div
      aria-hidden="true"
      class="pointer-events-none absolute left-1/2 top-0 h-px w-[100px] max-w-full -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-transparent via-slate-400 to-transparent"
    />
  </section>
</template>
