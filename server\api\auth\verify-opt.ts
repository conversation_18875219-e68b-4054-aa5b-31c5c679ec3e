export default defineEventHandler(async (event) => {
  const { email, opt } = await readBody(event)

  if (!opt) {
    throw createError({ statusCode: 400, statusMessage: 'Missing opt' })
  }

  const user = await db.user.findUnique({ where: { email: email }})

  if (!user) {
    throw createError({ statusCode: 400, statusMessage: 'User not found' })
  }

  const oneTimePassword = await db.oneTimePassword.findFirst({ 
    where: { 
      userId: user.id, 
      code: opt 
    }
  })

  if (!oneTimePassword) {
    throw createError({ statusCode: 400, statusMessage: 'Invalid email or code' })
  }

  if (!isWithinExpiryDate(oneTimePassword.codeExpiresAt.getTime())) {
    throw createError({ statusCode: 400, statusMessage: 'Verification code has expired' })
  }

  await db.user.update({
    where: {
      id: user.id,
    },
    data: {
      emailVerifiedAt: user.emailVerifiedAt || new Date(),
      lastLoginAt: new Date(),
    },
  })

  await db.oneTimePassword.delete({ where: { id: oneTimePassword.id }})

  const transformedUser = sanitizeUser(user)
  await setUserSession(event, { user: transformedUser })
})
