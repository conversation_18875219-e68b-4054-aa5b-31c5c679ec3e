import tailwindcss from '@tailwindcss/vite'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: false },
  debug: false,

  future: {
    compatibilityVersion: 4,
  },
  
  modules: [
    '@vueuse/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/color-mode',
    '@nuxt/eslint',
    'nuxt-auth-utils',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/fonts',
  ],

  // * Note that this option will not override the default directories (~/components, ~/composables, ~/middleware, ~/utils).
  imports: {
    dirs: [
      '../stores',
      '../app/utils',
    ],
  },

  fonts: {
    providers: {
      google: false,
      googleicons: false,
    },
    priority: ['bunny', 'fontsource', 'local']
  },

  // vue: {
  //   compilerOptions: {
  //     isCustomElement: tag => ['iconify-icon'].includes(tag),
  //   },
  // },

  colorMode: {
    preference: 'dark', // default value of $colorMode.preference -> 'system'
    fallback: 'dark', // fallback value if not system preference found
    classSuffix: '',
  },

  // content: {
  //   // highlight: {
  //   //   // Theme used in all color schemes.
  //   //   theme: 'github-dark-high-contrast',
  //   //   // OR
  //   //   // theme: {
  //   //   //   // Default theme (same as single string)
  //   //   //   default: 'vitesse-dark',
  //   //   //   // Theme used if `html.dark`
  //   //   //   dark: 'github-dark-high-contrast',
  //   //   //   // Theme used if `html.sepia`
  //   //   //   sepia: 'monokai'
  //   //   // }
  //   // },
  // },

  runtimeConfig: {
    fromEmail: 'gomark.pro <<EMAIL>>',
    public: {
      brand: 'Gomark',
      contactEmail: '<EMAIL>',
      baseUrl: process.env.NODE_ENV === 'development' ? `http://localhost:${process.env.PORT ?? 3000}` : 'https://gomark.pro',
    },
  },

  // Build as SPA application
  // ssr: false,

  routeRules: {
    '/admin/**': { ssr: false },
    '/dashboard/**': { ssr: false },
    // '/api/**': { cors: true },
  },

  // 分层加载CSS以优化HMR性能
  // 基础样式 -> UI组件样式 -> 页面样式
  css: [
    '~/assets/css/tailwind.base.css',    // 基础样式：TailwindCSS核心、主题、字体
    '~/assets/css/tailwind.ui.css',      // UI组件样式：components/ui目录
    '~/assets/css/tailwind.pages.css',   // 页面样式：其他组件和自定义样式
  ],
  vite: {
    plugins: [
      {
        name: 'strip-css-map-dev',
        apply: 'serve',
        enforce: 'post',
        transform(code, id) {
          if (id.includes('tailwind.css') && id.endsWith('.css')) {
            return {
              code: code.replace(/\/\*#\s*sourceMappingURL=.*\*\//gm, ''),
              map: null // 强制不传 map
            }
          }
        }
      },
      tailwindcss(),
      {
        name: 'strip-css-map-dev2',
        apply: 'serve',
        enforce: 'post',
        transform(code, id) {
          if (id.includes('tailwind.css') && id.endsWith('.css')) {
            return {
              code: code.replace(/\/\*#\s*sourceMappingURL=.*\*\//gm, ''),
              map: null // 强制不传 map
            }
          }
        }
      },
    ],
  },

  // sourcemap: { server: false, client: false },

  // eslint: {
  //   config: {
  //     stylistic: true,
  //   },
  // },

  icon: {
    mode: 'svg',
    customCollections: [
      {
        prefix: 'custom',
        dir: './app/assets/custom-icons'
      },
    ],
  },
  
  shadcn: {
    prefix: '',
    componentDir: '@/components/ui',
  },
})
