<script setup lang="ts">
interface Product {
  name: string
  variants: Array<{
    id: string
    interval: string
    price: number
  }>
}

const plans = [
  {
    name: 'Free',
    features: [
      { title: 'Unlimited cards', active: true },
      { title: 'Up to 10 boards per Workspace', active: true },
      { title: 'Unlimited Power-Ups per board', active: true },
      { title: 'Unlimited storage (10MB/file)', active: true },
      { title: '250 Workspace command runs', active: false },
      { title: 'Custom backgrounds & stickers', active: false },
      { title: 'Unlimited activity log', active: false },
      { title: 'Assignee and due dates', active: false },
      { title: '2-factor authentication', active: false },
    ],
  },
  {
    name: 'Basic',
    features: [
      { title: 'Unlimited boards', active: true },
      { title: 'Advanced checklists', active: true },
      { title: 'Custom Fields', active: true },
      { title: 'Unlimited storage (250MB/file)', active: true },
      { title: '1,000 Workspace command runs', active: true },
      { title: 'Single board guests', active: false },
      { title: 'Saved searches', active: false },
      { title: 'Assignee and due dates', active: false },
      { title: 'Ticket Support', active: false },
    ],
  },
  {
    name: 'Pro',
    features: [
      { title: 'Unlimited Workspaces', active: true },
      { title: 'Organization-wide permissions', active: true },
      { title: 'Organization-visible boards', active: true },
      { title: 'Public board management', active: true },
      { title: 'Multi-board guests', active: true },
      { title: 'Attachment permissions', active: true },
      { title: 'Power-Up administration', active: true },
      { title: 'Free SSO', active: true },
      { title: '24x7 Support', active: true },
    ],
  },
]

const interval = ref('month')

const mergePlansWithProducts = (products: Product[]) => {
  if (!products || !plans) {
    return []
  }
  return plans.map((plan) => {
    const product = products.find(prod => prod.name === plan.name)
    return { ...plan, product }
  })
}

const getPriceByInterval = (product: Product | undefined) => {
  const variant = (product?.variants ?? []).find(v => v.interval === interval.value)
  return variant?.price / 100 || 0
}

const getVariantByInterval = (product: Product | undefined) => {
  return (product?.variants ?? []).find(v => v.interval === interval.value)
}

const { data: products } = await useFetch<Product[]>('/api/payment/products')

const mergedPlans = mergePlansWithProducts(products.value ?? [])
</script>

<template>
  <Tabs
    v-model="interval"
    class="mb-10"
  >
    <TabsList>
      <TabsTrigger value="month">
        Month
      </TabsTrigger>
      <TabsTrigger value="year">
        Annual (Save 17%)
      </TabsTrigger>
    </TabsList>
  </Tabs>

  <div class="grid grid-cols-1 justify-center gap-3 lg:grid-cols-3">
    <div
      v-for="plan in mergedPlans"
      :key="plan.name"
      class="relative flex flex-col rounded-2xl border p-6 text-center *:z-10 md:hover:opacity-100"
      :class="{ 'border-primary': plan.name === 'Basic' }"
    >
      <div
        aria-hidden="true"
        class="pointer-events-none absolute -left-0.5 -top-0.5 size-[calc(100%_+_4px)] bg-gradient-to-b from-transparent to-slate-200 dark:to-background"
      />
      <h3 class="mb-6 flex min-h-16 items-center justify-center gap-2 text-base tracking-tight">
        <span>{{ plan.name }}</span>
      </h3>
      <span class="mb-6 flex items-center justify-center text-[2.25rem] font-normal leading-[1.7] tracking-tight">
        <Badge
          v-show="interval === 'year' && plan.product"
          size="xs"
          class="mr-2 rounded"
        >-17%</Badge>
        <span>${{ getPriceByInterval(plan.product) }} / {{ interval === 'month' ? 'mo' : 'yr' }}</span>
      </span>
      <div class="flex flex-1 flex-col justify-center border-y py-4">
        <p class="text-center text-sm font-normal leading-[1.6]">
          1,000 contacts / mo
        </p>
        <p class="mt-2 text-center text-sm font-normal leading-[1.6]">
          Unlimited email sending
        </p>
      </div>
      <ul class="mt-6 flex flex-col gap-3">
        <li
          v-for="(feature, index) in plan.features"
          :key="index"
          class="flex items-center gap-2 text-left "
        >
          <Icon
            name="lucide:circle-check"
            class="size-4"
            :class="feature.active ? 'text-emerald-600' : 'text-muted-foreground'"
          />
          <span class="text-sm font-normal leading-[1.6]">{{ feature.title }}</span>
        </li>
      </ul>
      <BillingCheckoutButton
        :variant-id="getVariantByInterval(plan.product)?.id"
        :is-active="false"
        class="mt-6"
      />
    </div>
  </div>
</template>
