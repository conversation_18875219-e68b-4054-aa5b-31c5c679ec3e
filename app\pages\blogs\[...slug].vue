<script setup lang="ts">
import { format as dateFormat } from 'date-fns'

const route = useRoute()

const { data: page } = await useAsyncData(route.path, () => queryContent(route.path).where({
  $and: [
    { draft: { $not: true } },
  ],
}).findOne())
if (!page.value) {
  throw createError({ statusCode: 404, statusMessage: 'Page not found', fatal: true })
}

useSeoMeta({
  title: page.value.title,
  ogTitle: `${page.value.title}`,
  description: page.value.description,
  ogDescription: page.value.description,
})
</script>

<template>
  <div class="container max-w-5xl px-6 py-16 md:max-w-5xl">
    <p class="mt-6 text-center text-sm text-muted-foreground">
      {{ dateFormat(page.date, "MMMM do, yyyy") }}
    </p>
    <h2 class="font-gradient mt-4 text-center text-5xl leading-tight tracking-tight md:text-5xl">
      {{ page.title }}
    </h2>
    <p class="mt-8 text-center text-muted-foreground md:text-lg md:leading-normal">
      {{ page.description }}
    </p>
    <div class="mt-6 flex flex-row items-center justify-center gap-2">
      <Avatar class="size-7 rounded-full">
        <AvatarImage
          :src="page.author?.avatarUrl || ''"
          :alt="page.author?.name"
        />
        <AvatarFallback class="rounded-full">
          {{ avatarName(page.author?.name) }}
        </AvatarFallback>
      </Avatar>
      <span class="text-sm text-muted-foreground">{{ page.author?.name }}</span>
    </div>

    <div class="prose prose-zinc mx-auto mt-32 max-w-3xl dark:prose-invert">
      <ContentRenderer
        v-if="page && page.body"
        :value="page"
        class=""
      />
    </div>
  </div>
</template>
