async function upsertOauthAccount(oauthAccount) {
  try {
    const user = await db.user.upsert({
      where: {
        email: oauthAccount.email,
      },
      update: {
        name: oauthAccount.name,
        avatarUrl: oauthAccount.avatarUrl,
      },
      create: {
        email: oauthAccount.email,
        name: oauthAccount.name,
        role: 'USER',
        avatarUrl: oauthAccount.avatarUrl,
        emailVerifiedAt: new Date(),
      },
    })

    await db.oauthAccount.upsert({
      where: {
        provider_providerAccountId: {
          provider: oauthAccount.provider,
          providerAccountId: oauthAccount.providerAccountId.toString(),
        },
      },
      update: {},
      create: {
        userId: user.id,
        provider: oauthAccount.provider,
        providerAccountId: oauthAccount.providerAccountId.toString(),
      },
    })

    return user
  }
  catch (error) {
    console.log(error)
    throw error.statusCode
      ? error
      : createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
      })
  }
}

// https://github.com/atinux/nuxt-auth-utils?tab=readme-ov-file#oauth-event-handlers
export default defineOAuthGitHubEventHandler({
  config: {
    emailRequired: true,
  },
  async onSuccess(event, { user }) {
    const oauthAccount = {
      email: user.email,
      name: user.name,
      avatarUrl: user.avatar_url,
      provider: 'github',
      providerAccountId: user.id,
    }
    const dbUser = await upsertOauthAccount(oauthAccount)

    if (dbUser.lockedAt) {
      throw createError({
        statusCode: 403,
        statusMessage: 'You account has been locked',
      })
    }
    const transformedUser = sanitizeUser(dbUser)
    await setUserSession(event, { user: transformedUser })
    return sendRedirect(event, '/dashboard')
  },
  onError(event, error) {
    console.error('GitHub OAuth error:', error)
    return sendRedirect(event, '/')
  },
})
