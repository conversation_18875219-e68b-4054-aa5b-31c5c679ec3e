import { sanitizeUser, generateOneTimePassword } from '@@/server/utils/auth'
import { sendEmail, sendOneTimePasswordTemplate } from '@@/server/libs/email'
import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const runtimeConfig = useRuntimeConfig()
  const schema = z.object({
    email: z.string().email(),
  })

  let optType = 'SIGNIN'

  const { email } = await readValidatedBody(event, body =>
    schema.parse(body),
  )
  const existingUser = await db.user.findUnique({ where: { email } })
  let user = existingUser
  
  if (!user) {
    user = await db.user.create({
      data: {
        email,
        role: 'USER',
      },
    })
    optType = 'SIGNUP'
  }

  // send email
  const optCode = await generateOneTimePassword({
    userId: user.id,
    type: optType,
    identifier: email,
  })

  const html = sendOneTimePasswordTemplate({
    email: email,
    brand: runtimeConfig.public.brand,
    link: `${runtimeConfig.public.baseUrl}/auth/verify-opt?email=${email}`,
    optCode: optCode,
  })

  await sendEmail({
    to: email,
    from: runtimeConfig.fromEmail,
    subject: `${runtimeConfig.public.brand} - Sign-In code`,
    html,
  })

  setResponseStatus(event, 201)
  return sanitizeUser(user)
})
