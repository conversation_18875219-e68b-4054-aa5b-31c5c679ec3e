export default defineEventHandler(async (event) => {
  try {
    const { token } = await readBody(event)

    if (!token) {
      throw createError({ statusCode: 400, statusMessage: 'Missing token' })
    }

    // const storedToken = await authActions.findEmailVerificationCode(token);
    const existingUser = await db.user.findUnique({ where: { emailVerificationCode: token } })

    if (!existingUser) {
      throw createError({ statusCode: 400, statusMessage: 'Invalid verification code' })
    }

    if (!existingUser.emailVerificationCodeExpiresAt) {
      throw createError({ statusCode: 400, statusMessage: 'Invalid expiry date' })
    }

    if (!isWithinExpiryDate(existingUser.emailVerificationCodeExpiresAt.getTime())) {
      throw createError({ statusCode: 400, statusMessage: 'Verification code has expired' })
    }

    await db.user.update({
      where: {
        id: existingUser.id,
      },
      data: {
        emailVerifiedAt: new Date(),
        emailVerificationCode: null,
        emailVerificationCodeExpiresAt: null,
        lastLoginAt: new Date(),
      },
    })

    const transformedUser = sanitizeUser(existingUser)
    await setUserSession(event, { user: transformedUser })
  }
  catch (error: any) {
    console.log(error)
    throw error.statusCode
      ? error
      : createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
      })
  }
})
