<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'

const route = useRoute()
const appConfig = useAppConfig()
const themeStore = useThemeStore()

const { loggedIn, fetch: refreshSession, user } = useUserSession()

const { init: initAnalytics } = useAnalytics()
initAnalytics()

useSeoMeta({
  title: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  description: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
  ogTitle: computed(() => route.meta.title ? `${appConfig.appName} - ${route.meta.title}` : `${appConfig.appName}`).value as string,
  ogDescription: computed(() => route.meta.description ? route.meta.description : `${appConfig.appDescription}`).value as string,
})

// 监听用户登录状态和属性是否变化，并通知扩展
watch(() => user.value, (newUser, oldUser) => {
  const isDeepEqual = JSON.stringify(newUser) === JSON.stringify(oldUser);
  if (!isDeepEqual) {
    window.postMessage({ type: 'website_to_ext', action: 'userChanged' })
  }
},{ deep: true })

onMounted(async () => {
  document.documentElement.style.setProperty('--radius', `${themeStore.radius}rem`)
  document.documentElement.classList.add(`theme-${themeStore.theme}`)
  if (loggedIn.value) {
    await $fetch('/api/account/refresh')
    await refreshSession()
    window.postMessage({ type: 'website_to_ext', action: 'userChanged' })
  }
})
</script>

<template>
  <TooltipProvider :delay-duration="0">
    <NuxtLoadingIndicator
      style="opacity: 1;"
      :height="2"
    />
    <Toaster
      position="top-center"
      :toastOptions="{
        classes: {
          error: '!bg-destructive',
          success: '',
          warning: '',
          info: '',
          toast: '!bg-background !text-foreground',
          title: 'font-semibold',
          description: '',
        },
      }"
    />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <ThemePopoverFixed />
  </TooltipProvider>
</template>
