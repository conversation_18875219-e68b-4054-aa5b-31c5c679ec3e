import { generateResetPasswordToken } from '@@/server/utils/auth'
import { sendEmail, resetPasswordTemplate } from '@@/server/libs/email'

export default defineEventHandler(async (event) => {
  const runtimeConfig = useRuntimeConfig()

  try {
    const { email } = await readBody(event)
    const user = await db.user.findUnique({ where: { email } })

    if (!user || !user.emailVerifiedAt) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email not found',
      })
    }

    const resetPasswordToken = await generateResetPasswordToken(user.id)
    const html = resetPasswordTemplate({
      brand: runtimeConfig.public.brand,
      link: `${runtimeConfig.public.baseUrl}/auth/change-password?token=${resetPasswordToken}`,
    })

    await sendEmail({
      to: email,
      from: runtimeConfig.fromEmail,
      subject: `${runtimeConfig.public.brand} - Resend Password`,
      html,
    })

    setResponseStatus(event, 201)
    return {
      status: 'success',
      message: 'Password reset link sent to your email',
    }
  }
  catch (error) {
    console.log(error)
    throw error.statusCode
      ? error
      : createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
      })
  }
})
