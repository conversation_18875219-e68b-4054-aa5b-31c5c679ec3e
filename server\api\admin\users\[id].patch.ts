import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const userId = getRouterParam(event, 'id')
  const { name, avatarUrl, lockedAt } = await readValidatedBody(event, body =>
    z.object({
      name: z.string().min(1).optional(),
      avatarUrl: z.string().optional(),
      lockedAt: z.string().optional(),
    }).parse(body),
  )

  const updatedUser = await db.user.update({
    where: {
      id: userId,
    },
    data: {
      name,
      avatarUrl,
      lockedAt,
    },
  })

  return updatedUser
})
