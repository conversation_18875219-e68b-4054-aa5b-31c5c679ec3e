export default defineEventHandler(async (event) => {
  // Get the current user session
  const { user } = await getUserSession(event)

  // console.log('New request url: ' + getRequestURL(event))
  if (event.node.req.url?.startsWith('/api/dashboard')) {
    if (!user) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden',
        message: 'You are not authorized to perform this action',
      })
    }
  }

  if (event.node.req.url?.startsWith('/api/admin')) {
    if (!user || user.role !== 'ADMIN') {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden',
        message: 'You are not authorized to perform this action',
      })
    }
  }
})
