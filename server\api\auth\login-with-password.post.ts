async function handleUserValidation(email: string, password: string) {
  const user = await db.user.findUnique({ where: { email } })

  if (!user || !user.hashedPassword) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid email or password',
    })
  }

  if (!user.hashedPassword && user.emailVerifiedAt) {
    throw createError({
      statusCode: 400,
      statusMessage: `Account connected with social login.`,
    })
  }

  const isPasswordCorrect = await verifyPassword(user.hashedPassword, password)
  if (!user || !isPasswordCorrect) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid email or password',
    })
  }

  if (!user.emailVerifiedAt) {
    throw createError({
      statusCode: 400,
      statusMessage: 'User has not verified their email',
    })
  }

  if (user.lockedAt) {
    throw createError({
      statusCode: 403,
      statusMessage: 'You account has been locked',
    })
  }
  return user
}

export default defineEventHandler(async (event) => {
  try {
    const { email, password } = await readBody(event)

    const user = await handleUserValidation(email, password)
    await db.user.update({
      where: {
        id: user.id,
      },
      data: {
        lastLoginAt: new Date(),
      },
    })

    await setUserSession(event, { user: sanitizeUser(user) })
    return sanitizeUser(user)
  }
  catch (error) {
    throw error
      ? error
      : createError({
        statusCode: 500,
        statusMessage: 'Internal Server Error',
      })
  }
})
