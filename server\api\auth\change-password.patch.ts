import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const forgotPasswordSchema = z.object({
    code: z.string(),
    password: z.string().min(6, 'Password must be at least 6 characters long'),
  })

  const { code, password } = await readValidatedBody(event, body =>
    forgotPasswordSchema.parse(body),
  )

  const user = await db.user.findUnique({
    where: {
      passwordResetCode: code,
      passwordResetCodeExpiresAt: {
        gte: new Date(),
      },
    },
  })

  if (!user) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Password reset link is invalid',
    })
  }

  const hashedPassword = await hashPassword(password)
  await db.user.update({
    where: {
      id: user.id,
    },
    data: {
      hashedPassword,
      passwordResetCode: null,
      passwordResetCodeExpiresAt: null,
    },
  })

  return { message: 'Password has been successfully reset' }
})
