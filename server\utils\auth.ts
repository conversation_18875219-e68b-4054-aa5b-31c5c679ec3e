import { customAlphabet, nanoid } from 'nanoid'

/**
 * Generates a new email verification code for a user and stores it in the database.
 *
 * @param {SelectUser} user - The user object containing user details.
 * @param {number} [expiresIn=10800000] - The time in milliseconds until the verification code expires. Defaults to 3 hours.
 * @returns {Promise<String>} - A promise that resolves to the generated email verification code.
 */

export const generateEmailVerificationCode = async (
  userId: string,
  expiresIn: number = 1000 * 60 * 60 * 3, // 3 hours default
): Promise<string> => {
  const code = nanoid(32)
  await db.user.update({
    where: {
      id: userId,
    },
    data: {
      emailVerificationCode: code,
      emailVerificationCodeExpiresAt: new Date(new Date().getTime() + expiresIn),
    },
  })
  return code
}

export const generateResetPasswordToken = async (
  userId: string,
  expiresIn: number = 1000 * 60 * 60 * 3, // 3 hours default
): Promise<string> => {
  const code = nanoid(32)
  await db.user.update({
    where: {
      id: userId,
    },
    data: {
      passwordResetCode: code,
      passwordResetCodeExpiresAt: new Date(new Date().getTime() + expiresIn),
    },
  })
  return code
}

const generateRandomString = (length: number, chars: string): string => {
  const alphabet = customAlphabet(chars, length)
  return alphabet()
}

export const generateOneTimePassword = async ({
  userId,
  type,
  identifier,
  expireDuration = 1000 * 60 * 10, // 10 minutes
}: {
  userId: string;
  type: string;
  identifier: string;
  expireDuration?: number;
}) => {
  const storedUserOpt = await db.oneTimePassword.findMany({
    where: {
      userId,
    },
  });

  if (storedUserOpt.length > 0) {
    const reusableStoredOpt = storedUserOpt.find((code) => {
      return isWithinExpiryDate(
        code.codeExpiresAt.getTime() - expireDuration / 2,
      );
    });
    if (reusableStoredOpt) {
      return reusableStoredOpt.code;
    }
  }

  const otp = generateRandomString(6, '0123456789');

  await db.oneTimePassword.create({
    data: {
      code: otp,
      type,
      identifier,
      codeExpiresAt: new Date(new Date().getTime() + expireDuration),
      userId,
    },
  });

  return otp;
};

/**
 * Checks if a time has expired.
 *
 * @param expiresAt - The expiresAt to check.
 * @returns True if the expiresAt has not expired, false otherwise.
 */
export function isWithinExpiryDate(expiresAt: number): boolean {
  const currentTime = Date.now()
  return currentTime < expiresAt
}

/*
 * This function is used to remove sensitive information from the user object
 *
 * @param user - The user object to sanitize
 * @returns The sanitized user object
 */
export const sanitizeUser = (user: any) => {
  if (!user) return null
  delete user.hashedPassword
  delete user.emailVerificationCode
  delete user.passwordResetCode
  return user
}
