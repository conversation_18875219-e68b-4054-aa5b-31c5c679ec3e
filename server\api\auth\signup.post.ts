import { sanitizeUser, generateEmailVerificationCode } from '@@/server/utils/auth'
import { sendEmail, confirmAccountTemplate } from '@@/server/libs/email'
import { z } from 'zod'

export default defineEventHandler(async (event) => {
  const runtimeConfig = useRuntimeConfig()
  const schema = z.object({
    name: z.string().min(4).max(255).optional().or(z.literal('')),
    email: z.string().email(),
    password: z.string().min(6, 'Must be at least 6 characters'),
  })

  const { email, name, password } = await readValidatedBody(event, body =>
    schema.parse(body),
  )
  const existingUser = await db.user.findUnique({ where: { email } })
  if (existingUser) {
    throw createError({
      statusCode: 400,
      statusMessage: 'User already exists',
    })
  }
  // createUser
  const hashedPassword = await hashPassword(password)
  const user = await db.user.create({
    data: {
      email,
      name,
      role: 'USER',
      hashedPassword,
    },
  })

  // send email
  const emailVerificationCode = await generateEmailVerificationCode(user.id)
  const html = confirmAccountTemplate({
    user: name || email,
    brand: runtimeConfig.public.brand,
    link: `${runtimeConfig.public.baseUrl}/auth/verify-email?token=${emailVerificationCode}`,
  })

  await sendEmail({
    to: email,
    from: runtimeConfig.fromEmail,
    subject: `${runtimeConfig.public.brand} - Confirm Your Account`,
    html,
  })

  setResponseStatus(event, 201)
  return sanitizeUser(user)
})
