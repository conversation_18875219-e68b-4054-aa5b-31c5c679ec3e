<script setup lang="ts">
import { format as dateFormat } from 'date-fns'

defineProps<{
  blog: any
}>()
</script>

<template>
  <NuxtLink
    :href="blog._path"
    class="group flex size-full flex-col border-b py-2 sm:py-4 md:grid md:grid-cols-12"
  >
    <div class="flex w-full md:col-span-8">
      <h3 class="text-lg group-hover:underline">
        {{ blog.title }}
      </h3>
    </div>
    <div class="flex grid-cols-2 items-center justify-start gap-2 text-sm md:col-span-4 md:grid md:grid-cols-2">
      <div class="hidden items-center gap-2 lg:flex">
        <Avatar class="size-6">
          <AvatarImage
            :src="`${blog.author?.avatarUrl}`"
            :alt="`${blog.author?.name} avatar`"
          />
          <AvatarFallback>{{ avatarName(blog.author?.name) }}</AvatarFallback>
        </Avatar>
        <span class="text-muted-foreground">{{ blog.author?.name }}</span>
      </div>
      <p
        v-if="blog.date"
        class="w-full flex-1 text-muted-foreground lg:text-right"
      >
        {{ dateFormat(blog.date, "MMM dd, yyyy") }}
      </p>
    </div>
  </NuxtLink>
</template>
